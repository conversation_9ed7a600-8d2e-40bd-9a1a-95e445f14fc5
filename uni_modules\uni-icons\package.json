{"id": "uni-icons", "displayName": "uni-icons 图标", "version": "2.0.12", "description": "图标组件，用于展示移动端常见的图标，可自定义颜色、大小。", "keywords": ["uni-ui", "uniui", "icon", "图标"], "repository": "https://github.com/dcloudio/uni-ui", "engines": {"HBuilderX": "^3.2.14", "uni-app": "^4.08", "uni-app-x": "^4.61"}, "directories": {"example": "../../temps/example_temps"}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "无", "permissions": "无"}, "npmurl": "https://www.npmjs.com/package/@dcloudio/uni-ui", "type": "component-vue", "darkmode": "x", "i18n": "x", "widescreen": "x"}, "uni_modules": {"dependencies": ["uni-scss"], "encrypt": [], "platforms": {"cloud": {"tcb": "x", "aliyun": "x", "alipay": "x"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "-", "android": {"extVersion": "", "minVersion": "29"}, "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "√", "toutiao": "√", "baidu": "√", "kuaishou": "-", "jd": "-", "harmony": "-", "qq": "√", "lark": "-"}, "quickapp": {"huawei": "√", "union": "√"}}, "uni-app-x": {"web": {"safari": "√", "chrome": "√"}, "app": {"android": {"extVersion": "", "minVersion": "29"}, "ios": "√", "harmony": "√"}, "mp": {"weixin": "√"}}}}}}