<template>
  <!-- 节点列表折叠控制 -->
  <view class="nodes-collapse-header" @click="toggleNodesCollapse">
    <view class="collapse-header-content">
      <wd-icon name="list" size="16px" color="#722ed1" />
      <text class="collapse-title">节点列表</text>
      <view class="nodes-summary">
        <text class="summary-text">{{ getNodesSummary() }}</text>
      </view>
    </view>
    <view class="nodes-collapse-icon" :class="{ collapsed: isNodesCollapsed }">
      <wd-icon name="arrow-down" size="16px" color="#722ed1" />
    </view>
  </view>

  <view class="node-cards-container" :class="{ 'nodes-collapsed': isNodesCollapsed }">
    <template v-for="item in getDisplayNodes()" :key="item.nodeCode">
      <view
        class="node-card"
        :class="{
          'node-completed': item.nodeData?.IsEnd,
          'node-empty': !item.nodeData,
          'node-preview': isNodesCollapsed
        }"
      >
        <!-- 卡片头部 -->
        <view class="node-card-header">
          <view class="node-info">
            <view class="node-number" :class="getNodeStatusClass(item)">{{ item.nodeCode }}</view>
            <view class="node-content-wrapper">
              <view class="node-title">{{ item.nodeName }}</view>
              <view class="node-subtitle" v-if="item.nodeData">
                <text class="completion-time">{{ formatDate(item.nodeData.CompletionTime) || '未设置完成时间' }}</text>
              </view>
            </view>
          </view>
          <view class="node-actions">
            <view class="node-status" :class="getNodeStatusClass(item)">
              <wd-icon :name="getNodeStatusIcon(item)" size="14px" :color="getNodeStatusColor(item)" />
            </view>
            <wd-switch v-if="item.nodeData && !isNodesCollapsed" v-model="item.nodeData.IsEnd" size="18px" @change="handleNodeStatusChange(item)" custom-class="compact-switch" />
          </view>
        </view>

        <!-- 卡片内容 - 仅当有nodeData时显示 -->
        <view class="node-card-content" v-if="item.nodeData && !isNodesCollapsed">
          <!-- 紧凑的编辑区域 -->
          <view class="compact-fields">
            <!-- 完成时间和备注在同一行 -->
            <view class="field-row">
              <view class="field-item flex-1">
                <view class="field-label-compact">
                  <wd-icon name="calendar" size="12px" color="#666" />
                  <text>完成时间</text>
                </view>
                <uni-datetime-picker :disabled="!item.nodeData.IsEnd" type="date" :clear-icon="false" v-model="item.nodeData.CompletionTime" />

                <!-- <wd-datetime-picker type="date" v-model="item.nodeData.CompletionTime" custom-class="compact-calendar" :disabled="!item.nodeData.IsEnd">
                  <view class="date-display-compact" :class="{ 'date-disabled': !item.nodeData.IsEnd }">
                    {{ formatDate(item.nodeData.CompletionTime) || '选择日期' }}
                  </view>
                </wd-datetime-picker> -->
              </view>
            </view>

            <!-- 备注信息 -->
            <view class="field-item" v-if="item.nodeData.IsEnd || item.nodeData.Remark">
              <view class="field-label-compact">
                <wd-icon name="edit" size="12px" color="#666" />
                <text>备注</text>
              </view>
              <wd-textarea v-model="item.nodeData.Remark" placeholder="请输入备注信息" :maxlength="100" auto-height :disabled="!item.nodeData.IsEnd" @change="handleRemarkChange(item)" custom-class="compact-textarea" />
            </view>
          </view>

          <!-- 必需文件列表 -->
          <view class="required-files" v-if="getRequiredFiles(item).length > 0">
            <view class="files-header" @click="toggleFilesCollapse(item)">
              <wd-icon name="folder" size="12px" color="#722ed1" />
              <text class="files-title">必需文件</text>
              <view class="files-count">{{ getRequiredFiles(item).length }}</view>
              <view class="collapse-icon" :class="{ collapsed: isFilesCollapsed(item) }">
                <wd-icon name="arrow-down" size="12px" color="#722ed1" />
              </view>
            </view>
            <view class="files-list" :class="{ 'files-collapsed': isFilesCollapsed(item) }">
              <view class="file-item" v-for="(file, index) in getRequiredFiles(item)" :key="file.FileCode || index">
                <view class="file-icon">{{ file.FileCode }}</view>
                <text class="file-name" :class="{ 'file-uploaded': file.UploadedPath }">{{ file.FileName }}</text>
                <view class="file-actions" v-if="false">
                  <view class="upload-status" v-if="file.UploadedPath">
                    <wd-icon name="check-circle" size="12px" color="#52c41a" />
                  </view>
                  <view class="upload-btn" @click="handleFileUpload(item, file)">
                    <wd-icon name="upload" size="12px" color="#722ed1" />
                  </view>
                </view>
              </view>
            </view>
          </view>

          <!-- 紧凑的元数据 -->
          <view class="node-meta-compact" v-if="item.nodeData.UpdateTime || item.nodeData.UpdatePerson">
            <text class="meta-text-compact">
              {{ formatDateTime(item.nodeData.UpdateTime) }}
              <text v-if="item.nodeData.UpdatePerson"> · {{ item.nodeData.UpdatePerson }}</text>
            </text>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<script setup>
import { ref } from 'vue'
import moment from 'moment'

// 设置 moment 中文本地化
moment.locale('zh-cn')

// Props
const props = defineProps({
  nodes: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['node-status-change', 'remark-change', 'file-upload'])

// 文件折叠状态管理
const filesCollapseState = ref(new Map())

// 节点列表折叠状态
const isNodesCollapsed = ref(true)

// 节点状态相关方法
function getNodeStatusClass(item) {
  if (!item.nodeData) return 'status-empty'
  return item.nodeData.IsEnd ? 'status-completed' : 'status-pending'
}

function getNodeStatusIcon(item) {
  if (!item.nodeData) return 'info'
  return item.nodeData.IsEnd ? 'check-circle' : 'clock'
}

function getNodeStatusColor(item) {
  if (!item.nodeData) return '#d9d9d9'
  return item.nodeData.IsEnd ? '#52c41a' : '#faad14'
}

// 处理节点状态变更
function handleNodeStatusChange(item) {
  if (!item.nodeData.CompletionTime) {
    item.nodeData.CompletionTime = moment().toISOString()
  }
  emit('node-status-change', item)
}

// 处理备注变更
function handleRemarkChange(item) {
  emit('remark-change', item)
}

// 格式化日期显示 - 使用 moment
function formatDate(dateStr) {
  if (!dateStr) return ''
  try {
    const momentDate = moment(dateStr)
    if (!momentDate.isValid()) return ''
    return momentDate.format('YYYY-MM-DD')
  } catch (error) {
    console.warn('formatDate error:', error)
    return ''
  }
}

// 格式化日期时间显示 - 使用 moment
function formatDateTime(dateStr) {
  if (!dateStr) return ''
  try {
    const momentDate = moment(dateStr)
    if (!momentDate.isValid()) return ''
    return momentDate.format('YYYY-MM-DD HH:mm')
  } catch (error) {
    console.warn('formatDateTime error:', error)
    return ''
  }
}

// 获取必需文件列表
function getRequiredFiles(item) {
  if (!item.nodeData || !item.nodeData.RequiredFiles) return []

  try {
    const files = JSON.parse(item.nodeData.RequiredFiles)
    return Array.isArray(files) ? files : []
  } catch (error) {
    return []
  }
}

// 文件折叠状态管理
function getFilesCollapseKey(item) {
  return `${item.nodeCode}_${item.nodeName}`
}

function isFilesCollapsed(item) {
  const key = getFilesCollapseKey(item)
  // 默认折叠状态为true
  return filesCollapseState.value.get(key) !== false
}

function toggleFilesCollapse(item) {
  const key = getFilesCollapseKey(item)
  const currentState = filesCollapseState.value.get(key)
  // 如果当前是undefined（默认折叠），则设置为false（展开）
  // 如果当前是false（展开），则设置为true（折叠）
  // 如果当前是true（折叠），则设置为false（展开）
  filesCollapseState.value.set(key, currentState === false ? true : false)
}

// 处理文件上传
function handleFileUpload(item, file) {
  emit('file-upload', item, file)
}

// 节点列表折叠管理
function toggleNodesCollapse() {
  isNodesCollapsed.value = !isNodesCollapsed.value
}

// 获取要显示的节点列表
function getDisplayNodes() {
  if (!props.nodes) return []

  if (isNodesCollapsed.value) {
    // 折叠状态下，只显示最后一个IsEnd为true的节点
    const completedNodes = props.nodes.filter((item) => item.nodeData?.IsEnd)
    if (completedNodes.length > 0) {
      // 返回最后一个完成的节点，并确保它是只读的
      const lastCompleted = { ...completedNodes[completedNodes.length - 1] }
      return [lastCompleted]
    }
    // 如果没有完成的节点，显示第一个有数据的节点
    const nodesWithData = props.nodes.filter((item) => item.nodeData)
    if (nodesWithData.length > 0) {
      return [{ ...nodesWithData[0] }]
    }
    // 如果都没有数据，显示第一个节点
    return props.nodes.slice(0, 1)
  }

  return props.nodes
}

// 获取节点摘要信息
function getNodesSummary() {
  if (!props.nodes) return '暂无数据'

  const total = props.nodes.length
  const completed = props.nodes.filter((item) => item.nodeData?.IsEnd).length
  const hasData = props.nodes.filter((item) => item.nodeData).length

  return `${completed}/${hasData} 已完成，共 ${total} 个节点`
}
</script>

<style lang="scss" scoped>
/* 节点列表折叠控制 */
.nodes-collapse-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx;
  margin: 0 20rpx 16rpx;
  background: linear-gradient(135deg, rgba(114, 46, 209, 0.05) 0%, rgba(146, 84, 222, 0.02) 100%);
  border: 1rpx solid rgba(114, 46, 209, 0.1);
  border-radius: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(135deg, rgba(114, 46, 209, 0.08) 0%, rgba(146, 84, 222, 0.04) 100%);
    border-color: rgba(114, 46, 209, 0.2);
  }
}

.collapse-header-content {
  display: flex;
  align-items: center;
  gap: 12rpx;
  flex: 1;
}

.collapse-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #722ed1;
}

.nodes-summary {
  margin-left: auto;
  margin-right: 16rpx;
}

.summary-text {
  font-size: 24rpx;
  color: #666666;
  background: rgba(114, 46, 209, 0.08);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.nodes-collapse-icon {
  transition: transform 0.3s ease;

  &.collapsed {
    transform: rotate(-90deg);
  }
}

/* 节点卡片样式 - 优化版 */
.node-cards-container {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.nodes-collapsed {
    max-height: 120rpx;
  }
}

.node-card {
  background: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
  border: 1rpx solid rgba(0, 0, 0, 0.04);
  margin: 0 20rpx;
  position: relative;
  transition: all 0.3s ease;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4rpx;
    background: linear-gradient(90deg, #f0f0f0 0%, #e0e0e0 100%);
  }

  &.node-completed::before {
    background: linear-gradient(90deg, #52c41a 0%, #73d13d 100%);
  }

  &.node-empty {
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    border-color: rgba(0, 0, 0, 0.02);
  }

  &.node-preview {
    cursor: default;
    pointer-events: none;
  }
}

.node-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 24rpx 16rpx;
  background: rgba(255, 255, 255, 0.8);
}

.node-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
}

.node-number {
  width: 48rpx;
  height: 48rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: 600;
  color: #ffffff;
  flex-shrink: 0;

  &.status-completed {
    background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  }

  &.status-pending {
    background: linear-gradient(135deg, #faad14 0%, #ffc53d 100%);
  }

  &.status-empty {
    background: rgba(217, 217, 217, 0.1);
  }
}

.node-content-wrapper {
  flex: 1;
  min-width: 0;
}

.node-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.node-subtitle {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.completion-time {
  font-size: 22rpx;
  color: #8c8c8c;
}

.node-actions {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.node-status {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  &.status-completed {
    background: rgba(82, 196, 26, 0.1);
  }

  &.status-pending {
    background: rgba(250, 173, 20, 0.1);
  }

  &.status-empty {
    background: rgba(217, 217, 217, 0.1);
  }
}

.node-card-content {
  padding: 20rpx 24rpx 24rpx;
}

.compact-fields {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.field-row {
  display: flex;
  gap: 16rpx;
}

.field-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;

  &.flex-1 {
    flex: 1;
  }
}

.field-label-compact {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 24rpx;
  color: #666666;
  font-weight: 500;
}

.date-display-compact {
  padding: 16rpx 20rpx;
  background: #fafafa;
  border: 1rpx solid #e8e8e8;
  border-radius: 12rpx;
  font-size: 26rpx;
  color: #262626;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    border-color: #722ed1;
    background: #f9f0ff;
  }

  &.date-disabled {
    background: #f5f5f5;
    color: #bfbfbf;
    cursor: not-allowed;

    &:hover {
      border-color: #e8e8e8;
      background: #f5f5f5;
    }
  }
}

/* 必需文件样式 */
.required-files {
  margin-top: 20rpx;
  border: 1rpx solid #f0f0f0;
  border-radius: 12rpx;
  overflow: hidden;
}

.files-header {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 16rpx 20rpx;
  background: #fafafa;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: #f0f0f0;
  }
}

.files-title {
  font-size: 24rpx;
  color: #722ed1;
  font-weight: 500;
  flex: 1;
}

.files-count {
  background: #722ed1;
  color: #ffffff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

.collapse-icon {
  transition: transform 0.3s ease;

  &.collapsed {
    transform: rotate(-90deg);
  }
}

.files-list {
  max-height: 400rpx;
  overflow: hidden;
  transition: max-height 0.3s ease;

  &.files-collapsed {
    max-height: 0;
  }
}

.file-item {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding: 16rpx 20rpx;
  border-top: 1rpx solid #f0f0f0;

  &:first-child {
    border-top: none;
  }
}

.file-icon {
  width: 32rpx;
  height: 32rpx;
  background: #722ed1;
  color: #ffffff;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
  flex-shrink: 0;
}

.file-name {
  flex: 1;
  font-size: 24rpx;
  color: #595959;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  &.file-uploaded {
    color: #52c41a;
  }
}

.file-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.upload-status {
  width: 24rpx;
  height: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-btn {
  width: 32rpx;
  height: 32rpx;
  background: rgba(114, 46, 209, 0.1);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: rgba(114, 46, 209, 0.2);
  }
}

/* 元数据样式 */
.node-meta-compact {
  margin-top: 20rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
}

.meta-text-compact {
  font-size: 20rpx;
  color: #999999;
  line-height: 1.4;
}
</style>
