<template>
  <div class="task-create-container">
    <!-- 选取修改字段 -->
    <div class="page-title">创建泵房核查任务</div>

    <div class="mar-T24">
      <wd-textarea label="任务描述" label-width="120rpx" v-model="params.TaskRemark" auto-height placeholder="请填写任务描述..." />
    </div>

    <div class="mar-Y24">
      <!-- <wd-calendar custom-class="task-create-calendar" label="任务结束时间" label-width="180rpx" type="datetime" v-model="params.TaskEndTime" @confirm="handleConfirm" /> -->
      <uni-datetime-picker type="datetime" placeholder="请选择任务结束时间" :clear-icon="false" v-model="params.TaskEndTime" @maskClick="maskClick" />
    </div>

    <div class="content">
      <div class="content_title">选择修改字段</div>
      <div class="flex f-wrap">
        <template v-for="item in data" :key="item.key">
          <div class="create-item" :class="{ 'create-item--selected': selectedFields.includes(item.key) }" @click="toggleField(item.key)">
            {{ item.value }}
          </div>
        </template>
      </div>
    </div>

    <div class="text-center mar-Y24">
      <wd-button type="success" @click="tackCreate" custom-style="width:90%">创建</wd-button>
    </div>
  </div>
  <wd-toast />
</template>

<script setup>
import { ref } from 'vue'
import { PumpHouseApi } from '@/services/model/pump.house'
import { useToast } from 'wot-design-uni'
import moment from 'moment'

// 设置 moment 中文本地化
moment.locale('zh-cn')

const props = defineProps({ data: { type: Array, default: () => [] } })
const emit = defineEmits('finished')
const params = ref({ TaskContent: '', TaskEndTime: '', TaskRemark: '' })
const toast = useToast()

const selectedFields = ref([])

// 切换字段选择状态
function toggleField(fieldKey) {
  const index = selectedFields.value.indexOf(fieldKey)
  if (index > -1) {
    selectedFields.value.splice(index, 1)
  } else {
    selectedFields.value.push(fieldKey)
  }
}

// 处理日期确认
function handleConfirm({ value }) {
  try {
    const momentDate = moment(value)
    if (!momentDate.isValid()) {
      console.warn('Invalid date value:', value)
      return
    }
    const formattedDate = momentDate.format('YYYY-MM-DD HH:mm:ss')
    params.value.TaskEndTime = formattedDate
  } catch (error) {
    console.warn('handleConfirm error:', error)
  }
}

// 创建任务
async function tackCreate() {
  if (selectedFields.value.length < 1) return toast.warning('请至少选择一个字段')
  if (params.value.TaskEndTime < 1) return toast.warning('请选中任务结束时间')
  params.value.TaskContent = selectedFields.value.join(',')

  try {
    toast.loading('正在创建...')
    PumpHouseApi.tackCreate(params.value)
    toast.close()
    toast.success('创建成功')
    emit('finished')
  } catch (error) {
    toast.close()
    toast.error('创建失败')
  }
  console.log(params.value)
}
</script>

<style lang="less" scoped>
.task-create-container {
  width: 100%;
  height: 100%;
  padding: 24rpx;
  background-color: aliceblue;

  .page-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 24rpx;
  }

  .content {
    .content_title {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 16rpx;
    }
  }

  .create-item {
    margin: 6rpx;
    padding: 12rpx 16rpx;
    border-radius: 9rpx;
    font-size: 24rpx;
    background-color: #f5f5f5;
    color: #999;
    border: 2rpx solid #e0e0e0;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;

    &:hover {
      background-color: #f0f0f0;
    }

    &--selected {
      background-color: #e8f5e8;
      color: #52c41a;
      border-color: #52c41a;
    }
  }
}
</style>

<style lang="less">
/* 任务创建页面专用的日历样式 - 覆盖全局样式，确保在微信小程序中生效 */
.task-create-container {
  /* 日历组件整体样式重置 */
  .task-create-calendar {
    /* 日历标签样式 */
    .wd-calendar__label {
      color: #333 !important;
      font-weight: 500 !important;
    }

    /* 日历显示值样式 - 覆盖全局白色文字 */
    .wd-calendar__value {
      color: #666 !important;
      background: transparent !important;
    }

    /* 日历箭头样式 - 覆盖全局白色箭头 */
    .wd-calendar__arrow,
    .wd-icon-arrow-right {
      color: #333 !important;
    }

    /* 日历头部样式重置 */
    .wd-calendar__header {
      background: #fff !important;
      color: #333 !important;
    }

    /* 日历标题样式重置 */
    .wd-calendar__title {
      color: #333 !important;
    }

    /* 日历主体样式重置 */
    .wd-calendar__body {
      background: #fff !important;
    }

    /* 日历日期样式重置 */
    .wd-calendar__day {
      color: #333 !important;
      background: #fff !important;
    }

    /* 选中日期样式 */
    .wd-calendar__day.is-selected {
      background: #52c41a !important;
      color: #fff !important;
    }

    /* 今天日期样式 */
    .wd-calendar__day.is-today {
      background: #e6f7ff !important;
      color: #1890ff !important;
    }

    /* 确保日期时间选择器的文字颜色不被覆盖 */
    .wd-datetime-picker__label,
    .wd-datetime-picker__value {
      color: #333 !important;
    }

    /* 弹窗关闭按钮样式重置 */
    .wd-popup__close {
      color: #333 !important;
    }
  }

  /* 针对微信小程序的特殊处理 */
  .wd-calendar {
    .wd-calendar__value {
      color: #666 !important;
    }

    .wd-calendar__arrow {
      color: #333 !important;
    }

    .wd-calendar__header {
      background: #fff !important;
      color: #333 !important;
    }

    .wd-calendar__title {
      color: #333 !important;
    }

    .wd-calendar__body {
      background: #fff !important;
    }

    .wd-calendar__day {
      color: #333 !important;
      background: #fff !important;
    }
  }
}
</style>
